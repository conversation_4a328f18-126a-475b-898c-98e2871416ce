const { withNativeWind } = require('nativewind/metro');
const { getDefaultConfig } = require('expo/metro-config');
const fs = require('fs');
const path = require('path');

const config = getDefaultConfig(__dirname);
config.resolver.blockList = [/(.*.test.tsx?)$/];

// Handle optional dev.config.json file
const devConfigPath = path.resolve(__dirname, 'dev.config.json');
const devConfigExists = fs.existsSync(devConfigPath);

if (!devConfigExists) {
  // Create a fallback dev.config.json if it doesn't exist
  config.resolver.alias = {
    ...config.resolver.alias,
    './dev.config.json': path.resolve(__dirname, 'src/config/dev.config.fallback.js'),
  };
}

module.exports = withNativeWind(config, { input: './global.css' });
